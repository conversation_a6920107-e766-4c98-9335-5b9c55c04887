apiVersion: v2
name: tuna
description: A Helm chart for Tuna application with web and API components
type: application
version: 0.1.0
appVersion: "1.0.0"
home: https://github.com/nxest/tuna
sources:
  - https://github.com/nxest/tuna
maintainers:
  - name: Tuna Team
    email: <EMAIL>
keywords:
  - tuna
  - web
  - api
  - application
dependencies:
  - name: keycloak
    version: "~21.4.0"
    repository: "https://charts.bitnami.com/bitnami"
    condition: keycloak.enabled
  - name: postgresql
    version: "~15.5.0"
    repository: "https://charts.bitnami.com/bitnami"
    condition: postgresql.enabled
  - name: oauth2-proxy
    version: "~7.0.0"
    repository: "https://charts.bitnami.com/bitnami"
    condition: oauth2-proxy.enabled
annotations:
  category: Application
