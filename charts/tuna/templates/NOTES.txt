1. Get the application URLs by running these commands:

{{- if .Values.tunaWeb.enabled }}
{{- if .Values.tunaWeb.ingress.enabled }}
{{- range $host := .Values.tunaWeb.ingress.hosts }}
  {{- range .paths }}
  http{{ if $.Values.tunaWeb.ingress.tls }}s{{ end }}://{{ $host.host }}{{ .path }}
  {{- end }}
{{- end }}
{{- else if contains "NodePort" .Values.tunaWeb.service.type }}
  export NODE_PORT=$(kubectl get --namespace {{ .Release.Namespace }} -o jsonpath="{.spec.ports[0].nodePort}" services {{ include "tuna.tunaWeb.fullname" . }})
  export NODE_IP=$(kubectl get nodes --namespace {{ .Release.Namespace }} -o jsonpath="{.items[0].status.addresses[0].address}")
  echo http://$NODE_IP:$NODE_PORT
{{- else if contains "LoadBalancer" .Values.tunaWeb.service.type }}
     NOTE: It may take a few minutes for the LoadBalancer IP to be available.
           You can watch the status of by running 'kubectl get --namespace {{ .Release.Namespace }} svc -w {{ include "tuna.tunaWeb.fullname" . }}'
  export SERVICE_IP=$(kubectl get svc --namespace {{ .Release.Namespace }} {{ include "tuna.tunaWeb.fullname" . }} --template "{{"{{ range (index .status.loadBalancer.ingress 0) }}{{.}}{{ end }}"}}")
  echo http://$SERVICE_IP:{{ .Values.tunaWeb.service.port }}
{{- else if contains "ClusterIP" .Values.tunaWeb.service.type }}
  export POD_NAME=$(kubectl get pods --namespace {{ .Release.Namespace }} -l "{{ include "tuna.tunaWeb.selectorLabels" . }}" -o jsonpath="{.items[0].metadata.name}")
  export CONTAINER_PORT=$(kubectl get pod --namespace {{ .Release.Namespace }} $POD_NAME -o jsonpath="{.spec.containers[0].ports[0].containerPort}")
  echo "Visit http://127.0.0.1:8080 to use your application"
  kubectl --namespace {{ .Release.Namespace }} port-forward $POD_NAME 8080:$CONTAINER_PORT
{{- end }}
{{- end }}

{{- if .Values.tunaApi.enabled }}
{{- if .Values.tunaApi.ingress.enabled }}
{{- range $host := .Values.tunaApi.ingress.hosts }}
  {{- range .paths }}
  API: http{{ if $.Values.tunaApi.ingress.tls }}s{{ end }}://{{ $host.host }}{{ .path }}
  {{- end }}
{{- end }}
{{- else if contains "NodePort" .Values.tunaApi.service.type }}
  export API_NODE_PORT=$(kubectl get --namespace {{ .Release.Namespace }} -o jsonpath="{.spec.ports[0].nodePort}" services {{ include "tuna.tunaApi.fullname" . }})
  export NODE_IP=$(kubectl get nodes --namespace {{ .Release.Namespace }} -o jsonpath="{.items[0].status.addresses[0].address}")
  echo API: http://$NODE_IP:$API_NODE_PORT
{{- else if contains "LoadBalancer" .Values.tunaApi.service.type }}
     NOTE: It may take a few minutes for the LoadBalancer IP to be available.
           You can watch the status of by running 'kubectl get --namespace {{ .Release.Namespace }} svc -w {{ include "tuna.tunaApi.fullname" . }}'
  export API_SERVICE_IP=$(kubectl get svc --namespace {{ .Release.Namespace }} {{ include "tuna.tunaApi.fullname" . }} --template "{{"{{ range (index .status.loadBalancer.ingress 0) }}{{.}}{{ end }}"}}")
  echo API: http://$API_SERVICE_IP:{{ .Values.tunaApi.service.port }}
{{- else if contains "ClusterIP" .Values.tunaApi.service.type }}
  export API_POD_NAME=$(kubectl get pods --namespace {{ .Release.Namespace }} -l "{{ include "tuna.tunaApi.selectorLabels" . }}" -o jsonpath="{.items[0].metadata.name}")
  export API_CONTAINER_PORT=$(kubectl get pod --namespace {{ .Release.Namespace }} $API_POD_NAME -o jsonpath="{.spec.containers[0].ports[0].containerPort}")
  echo "Visit http://127.0.0.1:8081 to use your API"
  kubectl --namespace {{ .Release.Namespace }} port-forward $API_POD_NAME 8081:$API_CONTAINER_PORT
{{- end }}
{{- end }}

2. Dependencies:
{{- if .Values.keycloak.enabled }}
   - Keycloak is enabled for authentication
{{- end }}
{{- if .Values.postgresql.enabled }}
   - PostgreSQL is enabled for database
{{- end }}
{{- if index .Values "oauth2-proxy" "enabled" }}
   - OAuth2 Proxy is enabled for authentication proxy
{{- end }}

3. To get the admin credentials for dependencies:
{{- if .Values.keycloak.enabled }}
   Keycloak admin password:
   kubectl get secret --namespace {{ .Release.Namespace }} {{ .Release.Name }}-keycloak -o jsonpath="{.data.admin-password}" | base64 --decode
{{- end }}
{{- if .Values.postgresql.enabled }}
   PostgreSQL password:
   kubectl get secret --namespace {{ .Release.Namespace }} {{ .Release.Name }}-postgresql -o jsonpath="{.data.postgres-password}" | base64 --decode
{{- end }}
