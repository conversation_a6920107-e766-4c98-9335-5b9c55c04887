1. Get the application URLs by running these commands:

{{- if .Values.ingress.enabled }}
{{- range $host := .Values.ingress.hosts }}
  {{- range .paths }}
  {{- if eq .service "web" }}
  Web: http{{ if $.Values.ingress.tls }}s{{ end }}://{{ $host.host }}{{ .path }}
  {{- else if eq .service "api" }}
  API: http{{ if $.Values.ingress.tls }}s{{ end }}://{{ $host.host }}{{ .path }}
  {{- end }}
  {{- end }}
{{- end }}
{{- else }}
{{- if .Values.tunaWeb.enabled }}
  # Tuna Web
  export WEB_POD_NAME=$(kubectl get pods --namespace {{ .Release.Namespace }} -l "{{ include "tuna.tunaWeb.selectorLabels" . }}" -o jsonpath="{.items[0].metadata.name}")
  echo "Visit http://127.0.0.1:8080 to use Tuna Web"
  kubectl --namespace {{ .Release.Namespace }} port-forward $WEB_POD_NAME 8080:3000
{{- end }}

{{- if .Values.tunaApi.enabled }}
  # Tuna API
  export API_POD_NAME=$(kubectl get pods --namespace {{ .Release.Namespace }} -l "{{ include "tuna.tunaApi.selectorLabels" . }}" -o jsonpath="{.items[0].metadata.name}")
  echo "Visit http://127.0.0.1:8081 to use Tuna API"
  kubectl --namespace {{ .Release.Namespace }} port-forward $API_POD_NAME 8081:8080
{{- end }}
{{- end }}

2. Application components:
{{- if .Values.tunaWeb.enabled }}
   - Tuna Web is enabled (Frontend)
{{- end }}
{{- if .Values.tunaApi.enabled }}
   - Tuna API is enabled (Backend)
{{- end }}
