{{/*
Expand the name of the chart.
*/}}
{{- define "tuna.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}
{{- define "tuna.fullname" -}}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- if contains $name .Release.Name }}
{{- .Release.Name | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "tuna.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "tuna.labels" -}}
helm.sh/chart: {{ include "tuna.chart" . }}
{{ include "tuna.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- with .Values.commonLabels }}
{{ toYaml . }}
{{- end }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "tuna.selectorLabels" -}}
app.kubernetes.io/name: {{ include "tuna.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

{{/*
Create the name of the service account to use
*/}}
{{- define "tuna.serviceAccountName" -}}
{{- if .Values.serviceAccount.create }}
{{- default (include "tuna.fullname" .) .Values.serviceAccount.name }}
{{- else }}
{{- default "default" .Values.serviceAccount.name }}
{{- end }}
{{- end }}

{{/*
Tuna Web fullname
*/}}
{{- define "tuna.tunaWeb.fullname" -}}
{{- printf "%s-web" (include "tuna.fullname" .) | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Tuna Web labels
*/}}
{{- define "tuna.tunaWeb.labels" -}}
{{ include "tuna.labels" . }}
app.kubernetes.io/component: web
{{- end }}

{{/*
Tuna Web selector labels
*/}}
{{- define "tuna.tunaWeb.selectorLabels" -}}
{{ include "tuna.selectorLabels" . }}
app.kubernetes.io/component: web
{{- end }}

{{/*
Tuna API fullname
*/}}
{{- define "tuna.tunaApi.fullname" -}}
{{- printf "%s-api" (include "tuna.fullname" .) | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Tuna API labels
*/}}
{{- define "tuna.tunaApi.labels" -}}
{{ include "tuna.labels" . }}
app.kubernetes.io/component: api
{{- end }}

{{/*
Tuna API selector labels
*/}}
{{- define "tuna.tunaApi.selectorLabels" -}}
{{ include "tuna.selectorLabels" . }}
app.kubernetes.io/component: api
{{- end }}

{{/*
Return the proper image name for Tuna Web
*/}}
{{- define "tuna.tunaWeb.image" -}}
{{- $registryName := .Values.tunaWeb.image.registry -}}
{{- $repositoryName := .Values.tunaWeb.image.repository -}}
{{- $tag := .Values.tunaWeb.image.tag | toString -}}
{{- if .Values.global.imageRegistry }}
    {{- printf "%s/%s:%s" .Values.global.imageRegistry $repositoryName $tag -}}
{{- else -}}
    {{- printf "%s/%s:%s" $registryName $repositoryName $tag -}}
{{- end -}}
{{- end -}}

{{/*
Return the proper image name for Tuna API
*/}}
{{- define "tuna.tunaApi.image" -}}
{{- $registryName := .Values.tunaApi.image.registry -}}
{{- $repositoryName := .Values.tunaApi.image.repository -}}
{{- $tag := .Values.tunaApi.image.tag | toString -}}
{{- if .Values.global.imageRegistry }}
    {{- printf "%s/%s:%s" .Values.global.imageRegistry $repositoryName $tag -}}
{{- else -}}
    {{- printf "%s/%s:%s" $registryName $repositoryName $tag -}}
{{- end -}}
{{- end -}}

{{/*
Return the proper Docker Image Registry Secret Names
*/}}
{{- define "tuna.imagePullSecrets" -}}
{{- include "common.images.pullSecrets" (dict "images" (list .Values.tunaWeb.image .Values.tunaApi.image) "global" .Values.global) -}}
{{- end -}}

{{/*
Compile all warnings into a single message.
*/}}
{{- define "tuna.validateValues" -}}
{{- $messages := list -}}
{{- $messages := append $messages (include "tuna.validateValues.tunaWeb" .) -}}
{{- $messages := append $messages (include "tuna.validateValues.tunaApi" .) -}}
{{- $messages := without $messages "" -}}
{{- $message := join "\n" $messages -}}

{{- if $message -}}
{{-   printf "\nVALUES VALIDATION:\n%s" $message -}}
{{- end -}}
{{- end -}}

{{/*
Validate values of Tuna Web
*/}}
{{- define "tuna.validateValues.tunaWeb" -}}
{{- if and .Values.tunaWeb.enabled (not .Values.tunaWeb.image.repository) -}}
tuna: tunaWeb.image.repository
    A valid image repository is required when Tuna Web is enabled
{{- end -}}
{{- end -}}

{{/*
Validate values of Tuna API
*/}}
{{- define "tuna.validateValues.tunaApi" -}}
{{- if and .Values.tunaApi.enabled (not .Values.tunaApi.image.repository) -}}
tuna: tunaApi.image.repository
    A valid image repository is required when Tuna API is enabled
{{- end -}}
{{- end -}}
