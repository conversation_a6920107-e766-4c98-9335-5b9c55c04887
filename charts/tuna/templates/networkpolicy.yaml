{{- if .Values.networkPolicy.enabled }}
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{ include "tuna.fullname" . }}
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "tuna.labels" . | nindent 4 }}
spec:
  podSelector:
    matchLabels: {{- include "tuna.selectorLabels" . | nindent 6 }}
  policyTypes:
    - Ingress
    {{- if .Values.networkPolicy.egress }}
    - Egress
    {{- end }}
  {{- if .Values.networkPolicy.allowExternal }}
  ingress:
    - {}
  {{- else }}
  ingress:
    {{- with .Values.networkPolicy.ingress }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- end }}
  {{- if .Values.networkPolicy.egress }}
  egress:
    {{- with .Values.networkPolicy.egress }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- end }}
{{- end }}
