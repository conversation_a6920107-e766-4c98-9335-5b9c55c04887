{{- if .Values.rbac.create }}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "tuna.fullname" . }}
  labels: {{- include "tuna.labels" . | nindent 4 }}
rules:
{{- with .Values.rbac.rules }}
{{- toYaml . | nindent 2 }}
{{- end }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ include "tuna.fullname" . }}
  labels: {{- include "tuna.labels" . | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ include "tuna.fullname" . }}
subjects:
  - kind: ServiceAccount
    name: {{ include "tuna.serviceAccountName" . }}
    namespace: {{ .Release.Namespace }}
{{- end }}
