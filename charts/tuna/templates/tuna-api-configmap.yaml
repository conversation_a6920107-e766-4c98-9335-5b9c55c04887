{{- if and .Values.tunaApi.enabled .Values.tunaApi.configMap.enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "tuna.tunaApi.fullname" . }}-config
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "tuna.tunaApi.labels" . | nindent 4 }}
  {{- with .Values.commonAnnotations }}
  annotations: {{- toYaml . | nindent 4 }}
  {{- end }}
data:
  {{- with .Values.tunaApi.configMap.data }}
  {{- toYaml . | nindent 2 }}
  {{- end }}
{{- end }}
