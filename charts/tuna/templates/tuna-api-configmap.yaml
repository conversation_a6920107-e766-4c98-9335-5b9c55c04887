{{- if .Values.tunaApi.enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "tuna.tunaApi.fullname" . }}-config
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "tuna.tunaApi.labels" . | nindent 4 }}
  {{- with .Values.commonAnnotations }}
  annotations: {{- toYaml . | nindent 4 }}
  {{- end }}
data:
  {{- if .Values.postgresql.enabled }}
  db-url: "jdbc:postgresql://{{ .Release.Name }}-postgresql:5432/{{ .Values.postgresql.auth.database }}"
  db-username: {{ .Values.postgresql.auth.username | quote }}
  {{- else }}
  db-url: "jdbc:postgresql://{{ .Values.externalPostgres.host }}:{{ .Values.externalPostgres.port }}/{{ .Values.externalPostgres.database }}"
  db-username: {{ .Values.externalPostgres.username | quote }}
  {{- end }}
  # Keycloak configuration
  keycloak-server-url: {{ .Values.keycloak.serverUrl | quote }}
  keycloak-realm: {{ .Values.keycloak.realm | quote }}
  keycloak-client-id: {{ .Values.keycloak.clientId | quote }}
{{- end }}
