{{- if .Values.tunaApi.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "tuna.tunaApi.fullname" . }}
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "tuna.tunaApi.labels" . | nindent 4 }}
  {{- with .Values.commonAnnotations }}
  annotations: {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  replicas: {{ .Values.tunaApi.replicaCount }}
  selector:
    matchLabels: {{- include "tuna.tunaApi.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      labels: {{- include "tuna.tunaApi.selectorLabels" . | nindent 8 }}
      {{- with .Values.tunaApi.podLabels }}
      {{- toYaml . | nindent 8 }}
      {{- end }}
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/tuna-api-configmap.yaml") . | sha256sum }}
        {{- with .Values.tunaApi.podAnnotations }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.global.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tunaApi.image.pullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "tuna.serviceAccountName" . }}
      {{- with .Values.tunaApi.podSecurityContext }}
      securityContext:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
        - name: tuna-api
          {{- with .Values.tunaApi.containerSecurityContext }}
          securityContext:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          image: {{ include "tuna.tunaApi.image" . }}
          imagePullPolicy: {{ .Values.tunaApi.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.tunaApi.service.targetPort }}
              protocol: TCP
            - name: management
              containerPort: 8081
              protocol: TCP
          {{- if .Values.tunaApi.livenessProbe.enabled }}
          livenessProbe:
            httpGet:
              path: {{ .Values.tunaApi.livenessProbe.httpGet.path }}
              port: {{ .Values.tunaApi.livenessProbe.httpGet.port }}
            initialDelaySeconds: {{ .Values.tunaApi.livenessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.tunaApi.livenessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.tunaApi.livenessProbe.timeoutSeconds }}
            failureThreshold: {{ .Values.tunaApi.livenessProbe.failureThreshold }}
            successThreshold: {{ .Values.tunaApi.livenessProbe.successThreshold }}
          {{- end }}
          {{- if .Values.tunaApi.readinessProbe.enabled }}
          readinessProbe:
            httpGet:
              path: {{ .Values.tunaApi.readinessProbe.httpGet.path }}
              port: {{ .Values.tunaApi.readinessProbe.httpGet.port }}
            initialDelaySeconds: {{ .Values.tunaApi.readinessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.tunaApi.readinessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.tunaApi.readinessProbe.timeoutSeconds }}
            failureThreshold: {{ .Values.tunaApi.readinessProbe.failureThreshold }}
            successThreshold: {{ .Values.tunaApi.readinessProbe.successThreshold }}
          {{- end }}
          {{- with .Values.tunaApi.resources }}
          resources:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          env:
            {{- with .Values.tunaApi.env }}
            {{- toYaml . | nindent 12 }}
            {{- end }}
            # Database configuration
            - name: DB_URL
              valueFrom:
                configMapKeyRef:
                  name: {{ include "tuna.tunaApi.fullname" . }}-config
                  key: db-url
            - name: DB_USERNAME
              valueFrom:
                configMapKeyRef:
                  name: {{ include "tuna.tunaApi.fullname" . }}-config
                  key: db-username
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "tuna.tunaApi.fullname" . }}-secret
                  key: db-password
      {{- with .Values.tunaApi.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tunaApi.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tunaApi.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}
