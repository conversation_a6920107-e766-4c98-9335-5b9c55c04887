{{- if and .Values.tunaApi.enabled .Values.tunaApi.ingress.enabled }}
{{- $fullName := include "tuna.tunaApi.fullname" . -}}
{{- $svcPort := .Values.tunaApi.service.port -}}
{{- if and .Values.tunaApi.ingress.className (not (hasKey .Values.tunaApi.ingress.annotations "kubernetes.io/ingress.class")) }}
  {{- $_ := set .Values.tunaApi.ingress.annotations "kubernetes.io/ingress.class" .Values.tunaApi.ingress.className}}
{{- end }}
{{- if semverCompare ">=1.19-0" .Capabilities.KubeVersion.GitVersion -}}
apiVersion: networking.k8s.io/v1
{{- else if semverCompare ">=1.14-0" .Capabilities.KubeVersion.GitVersion -}}
apiVersion: networking.k8s.io/v1beta1
{{- else -}}
apiVersion: extensions/v1beta1
{{- end }}
kind: Ingress
metadata:
  name: {{ $fullName }}
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "tuna.tunaApi.labels" . | nindent 4 }}
  {{- with .Values.tunaApi.ingress.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  {{- if and .Values.tunaApi.ingress.className (semverCompare ">=1.18-0" .Capabilities.KubeVersion.GitVersion) }}
  ingressClassName: {{ .Values.tunaApi.ingress.className }}
  {{- end }}
  {{- if .Values.tunaApi.ingress.tls }}
  tls:
    {{- range .Values.tunaApi.ingress.tls }}
    - hosts:
        {{- range .hosts }}
        - {{ . | quote }}
        {{- end }}
      secretName: {{ .secretName }}
    {{- end }}
  {{- end }}
  rules:
    {{- range .Values.tunaApi.ingress.hosts }}
    - host: {{ .host | quote }}
      http:
        paths:
          {{- range .paths }}
          - path: {{ .path }}
            {{- if and .pathType (semverCompare ">=1.18-0" $.Capabilities.KubeVersion.GitVersion) }}
            pathType: {{ .pathType }}
            {{- end }}
            backend:
              {{- if semverCompare ">=1.19-0" $.Capabilities.KubeVersion.GitVersion }}
              service:
                name: {{ $fullName }}
                port:
                  number: {{ $svcPort }}
              {{- else }}
              serviceName: {{ $fullName }}
              servicePort: {{ $svcPort }}
              {{- end }}
          {{- end }}
    {{- end }}
{{- end }}
