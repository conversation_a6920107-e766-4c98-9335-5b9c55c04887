{{- if .Values.tunaApi.enabled }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "tuna.tunaApi.fullname" . }}-secret
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "tuna.tunaApi.labels" . | nindent 4 }}
  {{- with .Values.commonAnnotations }}
  annotations: {{- toYaml . | nindent 4 }}
  {{- end }}
type: Opaque
data:
  {{- if .Values.postgresql.enabled }}
  db-password: {{ .Values.postgresql.auth.password | default (randAlphaNum 10) | b64enc | quote }}
  {{- else }}
  db-password: {{ .Values.externalPostgres.password | b64enc | quote }}
  {{- end }}
{{- end }}
