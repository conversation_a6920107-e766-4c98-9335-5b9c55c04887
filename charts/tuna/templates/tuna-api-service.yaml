{{- if .Values.tunaApi.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "tuna.tunaApi.fullname" . }}
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "tuna.tunaApi.labels" . | nindent 4 }}
  {{- with .Values.tunaApi.service.annotations }}
  annotations: {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.tunaApi.service.type }}
  ports:
    - port: {{ .Values.tunaApi.service.port }}
      targetPort: {{ .Values.tunaApi.service.targetPort }}
      protocol: TCP
      name: http
  selector: {{- include "tuna.tunaApi.selectorLabels" . | nindent 4 }}
{{- end }}
