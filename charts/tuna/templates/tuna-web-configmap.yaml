{{- if and .Values.tunaWeb.enabled .Values.tunaWeb.configMap.enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "tuna.tunaWeb.fullname" . }}-config
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "tuna.tunaWeb.labels" . | nindent 4 }}
  {{- with .Values.commonAnnotations }}
  annotations: {{- toYaml . | nindent 4 }}
  {{- end }}
data:
  {{- with .Values.tunaWeb.configMap.data }}
  {{- toYaml . | nindent 2 }}
  {{- end }}
{{- end }}
