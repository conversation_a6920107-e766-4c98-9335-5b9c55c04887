{{- if .Values.tunaWeb.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "tuna.tunaWeb.fullname" . }}
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "tuna.tunaWeb.labels" . | nindent 4 }}
  {{- with .Values.commonAnnotations }}
  annotations: {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  replicas: {{ .Values.tunaWeb.replicaCount }}
  selector:
    matchLabels: {{- include "tuna.tunaWeb.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      labels: {{- include "tuna.tunaWeb.selectorLabels" . | nindent 8 }}
      {{- with .Values.tunaWeb.podLabels }}
      {{- toYaml . | nindent 8 }}
      {{- end }}
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/tuna-web-configmap.yaml") . | sha256sum }}
        {{- with .Values.tunaWeb.podAnnotations }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.global.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tunaWeb.image.pullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "tuna.serviceAccountName" . }}
      {{- with .Values.tunaWeb.podSecurityContext }}
      securityContext:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
        - name: tuna-web
          {{- with .Values.tunaWeb.containerSecurityContext }}
          securityContext:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          image: {{ include "tuna.tunaWeb.image" . }}
          imagePullPolicy: {{ .Values.tunaWeb.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.tunaWeb.service.targetPort }}
              protocol: TCP
          {{- if .Values.tunaWeb.livenessProbe.enabled }}
          livenessProbe:
            tcpSocket:
              port: {{ .Values.tunaWeb.livenessProbe.tcpSocket.port }}
            initialDelaySeconds: {{ .Values.tunaWeb.livenessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.tunaWeb.livenessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.tunaWeb.livenessProbe.timeoutSeconds }}
            failureThreshold: {{ .Values.tunaWeb.livenessProbe.failureThreshold }}
            successThreshold: {{ .Values.tunaWeb.livenessProbe.successThreshold }}
          {{- end }}
          {{- if .Values.tunaWeb.readinessProbe.enabled }}
          readinessProbe:
            tcpSocket:
              port: {{ .Values.tunaWeb.readinessProbe.tcpSocket.port }}
            initialDelaySeconds: {{ .Values.tunaWeb.readinessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.tunaWeb.readinessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.tunaWeb.readinessProbe.timeoutSeconds }}
            failureThreshold: {{ .Values.tunaWeb.readinessProbe.failureThreshold }}
            successThreshold: {{ .Values.tunaWeb.readinessProbe.successThreshold }}
          {{- end }}
          {{- with .Values.tunaWeb.resources }}
          resources:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- if or .Values.tunaWeb.env .Values.tunaWeb.configMap.enabled }}
          env:
            {{- with .Values.tunaWeb.env }}
            {{- toYaml . | nindent 12 }}
            {{- end }}
            {{- if .Values.tunaWeb.configMap.enabled }}
            - name: CONFIG_MAP_NAME
              value: {{ include "tuna.tunaWeb.fullname" . }}-config
            {{- end }}
          {{- end }}
          {{- if .Values.tunaWeb.configMap.enabled }}
          volumeMounts:
            - name: config
              mountPath: /app/config
              readOnly: true
          {{- end }}
      {{- if .Values.tunaWeb.configMap.enabled }}
      volumes:
        - name: config
          configMap:
            name: {{ include "tuna.tunaWeb.fullname" . }}-config
      {{- end }}
      {{- with .Values.tunaWeb.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tunaWeb.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tunaWeb.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}
