{{- if and .Values.tunaWeb.enabled .Values.tunaWeb.ingress.enabled }}
{{- $fullName := include "tuna.tunaWeb.fullname" . -}}
{{- $svcPort := .Values.tunaWeb.service.port -}}
{{- if and .Values.tunaWeb.ingress.className (not (hasKey .Values.tunaWeb.ingress.annotations "kubernetes.io/ingress.class")) }}
  {{- $_ := set .Values.tunaWeb.ingress.annotations "kubernetes.io/ingress.class" .Values.tunaWeb.ingress.className}}
{{- end }}
{{- if semverCompare ">=1.19-0" .Capabilities.KubeVersion.GitVersion -}}
apiVersion: networking.k8s.io/v1
{{- else if semverCompare ">=1.14-0" .Capabilities.KubeVersion.GitVersion -}}
apiVersion: networking.k8s.io/v1beta1
{{- else -}}
apiVersion: extensions/v1beta1
{{- end }}
kind: Ingress
metadata:
  name: {{ $fullName }}
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "tuna.tunaWeb.labels" . | nindent 4 }}
  {{- with .Values.tunaWeb.ingress.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  {{- if and .Values.tunaWeb.ingress.className (semverCompare ">=1.18-0" .Capabilities.KubeVersion.GitVersion) }}
  ingressClassName: {{ .Values.tunaWeb.ingress.className }}
  {{- end }}
  {{- if .Values.tunaWeb.ingress.tls }}
  tls:
    {{- range .Values.tunaWeb.ingress.tls }}
    - hosts:
        {{- range .hosts }}
        - {{ . | quote }}
        {{- end }}
      secretName: {{ .secretName }}
    {{- end }}
  {{- end }}
  rules:
    {{- range .Values.tunaWeb.ingress.hosts }}
    - host: {{ .host | quote }}
      http:
        paths:
          {{- range .paths }}
          - path: {{ .path }}
            {{- if and .pathType (semverCompare ">=1.18-0" $.Capabilities.KubeVersion.GitVersion) }}
            pathType: {{ .pathType }}
            {{- end }}
            backend:
              {{- if semverCompare ">=1.19-0" $.Capabilities.KubeVersion.GitVersion }}
              service:
                name: {{ $fullName }}
                port:
                  number: {{ $svcPort }}
              {{- else }}
              serviceName: {{ $fullName }}
              servicePort: {{ $svcPort }}
              {{- end }}
          {{- end }}
    {{- end }}
{{- end }}
