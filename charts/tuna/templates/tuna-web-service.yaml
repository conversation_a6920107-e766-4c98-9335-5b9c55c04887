{{- if .Values.tunaWeb.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "tuna.tunaWeb.fullname" . }}
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "tuna.tunaWeb.labels" . | nindent 4 }}
  {{- with .Values.tunaWeb.service.annotations }}
  annotations: {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.tunaWeb.service.type }}
  ports:
    - port: {{ .Values.tunaWeb.service.port }}
      targetPort: {{ .Values.tunaWeb.service.targetPort }}
      protocol: TCP
      name: http
  selector: {{- include "tuna.tunaWeb.selectorLabels" . | nindent 4 }}
{{- end }}
