# Default values for tuna.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

## Global parameters
global:
  imageRegistry: ""
  imagePullSecrets: []
  storageClass: ""

## Common parameters
nameOverride: ""
fullnameOverride: ""
kubeVersion: ""
clusterDomain: cluster.local
commonLabels: {}
commonAnnotations: {}

## Tuna Web configuration
tunaWeb:
  enabled: true
  image:
    registry: docker.io
    repository: nxest/tuna-web
    tag: ""
    pullPolicy: IfNotPresent
    pullSecrets: []
  
  replicaCount: 1
  
  ## Pod Security Context
  podSecurityContext:
    fsGroup: 1001
  
  ## Container Security Context
  containerSecurityContext:
    runAsUser: 1001
    runAsNonRoot: true
    readOnlyRootFilesystem: false
    allowPrivilegeEscalation: false
    capabilities:
      drop:
        - ALL
  
  ## Resource limits and requests
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 250m
      memory: 256Mi
  
  ## Liveness and readiness probes
  livenessProbe:
    enabled: true
    httpGet:
      path: /actuator/health/liveness
      port: management
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 5
    failureThreshold: 3
    successThreshold: 1

  readinessProbe:
    enabled: true
    httpGet:
      path: /actuator/health/readiness
      port: management
    initialDelaySeconds: 5
    periodSeconds: 5
    timeoutSeconds: 3
    failureThreshold: 3
    successThreshold: 1
  
  ## Service configuration
  service:
    type: ClusterIP
    port: 80
    targetPort: 3000
    annotations: {}
  

  
  ## Pod annotations
  podAnnotations: {}
  
  ## Pod labels
  podLabels: {}
  
  ## Node selector
  nodeSelector: {}
  
  ## Tolerations
  tolerations: []
  
  ## Affinity
  affinity: {}
  
  ## Environment variables
  env: []
  
  ## ConfigMap data
  configMap:
    enabled: false
    data: {}

## Tuna API configuration
tunaApi:
  enabled: true
  image:
    registry: docker.io
    repository: nxest/tuna-api
    tag: ""
    pullPolicy: IfNotPresent
    pullSecrets: []
  
  replicaCount: 1
  
  ## Pod Security Context
  podSecurityContext:
    fsGroup: 1001
  
  ## Container Security Context
  containerSecurityContext:
    runAsUser: 1001
    runAsNonRoot: true
    readOnlyRootFilesystem: false
    allowPrivilegeEscalation: false
    capabilities:
      drop:
        - ALL
  
  ## Resource limits and requests
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 250m
      memory: 256Mi
  
  ## Liveness and readiness probes
  livenessProbe:
    enabled: true
    httpGet:
      path: /health
      port: http
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 5
    failureThreshold: 3
    successThreshold: 1
  
  readinessProbe:
    enabled: true
    httpGet:
      path: /ready
      port: http
    initialDelaySeconds: 5
    periodSeconds: 5
    timeoutSeconds: 3
    failureThreshold: 3
    successThreshold: 1
  
  ## Service configuration
  service:
    type: ClusterIP
    port: 80
    targetPort: 8080
    annotations: {}
  

  
  ## Pod annotations
  podAnnotations: {}
  
  ## Pod labels
  podLabels: {}
  
  ## Node selector
  nodeSelector: {}
  
  ## Tolerations
  tolerations: []
  
  ## Affinity
  affinity: {}
  
  ## Environment variables
  env: []
  
  ## ConfigMap data
  configMap:
    enabled: false
    data: {}

## Unified Ingress configuration for both web and API
ingress:
  enabled: false
  className: ""
  annotations: {}
  hosts:
    - host: tuna.local
      paths:
        - path: /
          pathType: Prefix
          service: web
        - path: /tuna-api
          pathType: Prefix
          service: api
  tls: []

## ServiceAccount configuration
serviceAccount:
  create: true
  annotations: {}
  name: ""

## RBAC configuration
rbac:
  create: false
  rules: []

## Pod Disruption Budget
podDisruptionBudget:
  enabled: false
  minAvailable: 1
  maxUnavailable: ""

## Network Policy
networkPolicy:
  enabled: false
  allowExternal: true
  ingress: []
  egress: []


